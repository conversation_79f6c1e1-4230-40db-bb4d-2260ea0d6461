{"expo": {"name": "澳門考車神器", "slug": "macau-drive-exam", "owner": "248-labs", "version": "0.2.0", "orientation": "portrait", "icon": "./assets/images/icons/icon.png", "scheme": "macaudriveexam", "userInterfaceStyle": "automatic", "newArchEnabled": true, "locales": {"en": {"displayName": "Macau Drive Exam Saver"}, "zh-Hant": {"displayName": "澳門考車神器"}}, "ios": {"supportsTablet": false, "bundleIdentifier": "com.248labs.macaudrivesaver", "infoPlist": {"ITSAppUsesNonExemptEncryption": false, "CFBundleAllowMixedLocalizations": true}}, "android": {"edgeToEdgeEnabled": true}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/icons/icon.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#F0EDE6"}], ["expo-sqlite", {"enableFTS": true, "useSQLCipher": true, "android": {"enableFTS": true, "useSQLCipher": true}, "ios": {"customBuildFlags": ["-DSQLITE_ENABLE_DBSTAT_VTAB=1 -DSQLITE_ENABLE_SNAPSHOT=1"]}}]], "experiments": {"typedRoutes": true}, "extra": {"router": {}, "eas": {"projectId": "4190d37b-d565-4059-88aa-6c3a9a5639d6"}}}}