import { router } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import React, { useEffect, useState } from 'react';
import { Al<PERSON>, ScrollView, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

import { Ionicons } from '@expo/vector-icons';
import { Button } from '../../src/components/common';
import {
  OptionsList,
  QuestionCard
} from '../../src/components/question';
import { useRealtimeAnswer } from '../../src/hooks/useRealtimeAnswer';
import { WrongQuestionsService } from '../../src/services/questions/wrongQuestionsService';
import { useWrongQuestionsStore } from '../../src/store/useWrongQuestionsStore';
import { COLORS } from '../../src/utils/constants';

export default function WrongQuestionsSessionScreen() {
  const [quickAnswerMode, setQuickAnswerMode] = useState(false);

  const {
    isActive,
    sessionId,
    questions,
    currentIndex,
    currentQuestion,
    selectedAnswer,
    showAnswer,
    isAnswerCorrect,
    stats,
    selectAnswer,
    submitAnswer,
    nextQuestion,
    removeCurrentQuestion,
    endSession,
  } = useWrongQuestionsStore();

  const { recordAnswer } = useRealtimeAnswer();

  // Minimalistic stats component (matching practice session)
  const renderMinimalisticStats = () => {
    const totalAnswered = stats.answeredCount;
    const correctCount = stats.correctCount;
    const wrongCount = stats.wrongCount;
    const correctRatio = totalAnswered > 0 ? correctCount / totalAnswered : 0;
    const remainingQuestions = Math.max(0, questions.length - currentIndex);

    return (
      <View style={styles.minimalisticStats}>
        <View style={styles.remainingContainer}>
          <Text style={styles.remainingText}>剩餘 {remainingQuestions}</Text>
        </View>
        <View style={styles.scoreContainer}>
          <Text style={styles.correctNumber}>{correctCount}</Text>
          <View style={styles.progressBarContainer}>
            <View style={styles.progressBarBg}>
              <View
                style={[
                  styles.progressBarCorrect,
                  { width: `${correctRatio * 100}%` },
                ]}
              />
            </View>
          </View>
          <Text style={styles.wrongNumber}>{wrongCount}</Text>
        </View>
      </View>
    );
  };

  useEffect(() => {
    if (!isActive || !sessionId) {
      router.replace('/');
    }
  }, [isActive, sessionId]);

  if (!isActive || !currentQuestion) {
    return null;
  }

  const handleAnswerSelect = (answerIndex: number) => {
    if (showAnswer) return;
    selectAnswer(answerIndex);

    if (quickAnswerMode) {
      handleSubmitAnswer(answerIndex);
    }
  };

  const handleSubmitAnswer = async (answer?: number) => {
    const finalAnswer = answer ?? selectedAnswer;
    if (finalAnswer === null || showAnswer) return;

    submitAnswer();

    // Record answer in database
    try {
      const correctOptionIndex = currentQuestion.options.findIndex(opt => opt.isCorrect);
      const selectedOptionLabel = ['A', 'B', 'C', 'D'][finalAnswer];
      const correctOptionLabel = ['A', 'B', 'C', 'D'][correctOptionIndex];
      
      await recordAnswer({
        questionId: currentQuestion.id,
        volume: currentQuestion.volume,
        chapter: 1, // Default chapter
        isCorrect: currentQuestion.options[finalAnswer]?.isCorrect || false,
        mode: 'practice', // Use practice mode for wrong questions review
        sessionId: sessionId!,
        selectedOption: selectedOptionLabel as 'A' | 'B' | 'C' | 'D',
        correctOption: correctOptionLabel as 'A' | 'B' | 'C' | 'D',
        timeSpent: 30, // Default time
      });

      // If answer is correct, remove from wrong questions
      if (currentQuestion.options[finalAnswer]?.isCorrect) {
        await WrongQuestionsService.removeFromWrongQuestions(currentQuestion.id, currentQuestion.volume);
      }
    } catch (error) {
      console.error('Failed to record answer:', error);
      // Don't show error to user, just log it
    }
  };

  const handleNext = () => {
    if (isAnswerCorrect && currentQuestion) {
      // Remove from current session if answered correctly
      removeCurrentQuestion();
    } else {
      nextQuestion();
    }

    // Check if all questions completed
    if (questions.length <= 1) {
      handleSessionComplete();
    } else if (isLastQuestion) {
      // If this is the last question, complete the review
      handleSessionComplete();
    }
  };

  const handleSessionComplete = () => {
    Alert.alert(
      '複習完成',
      `您已完成錯題複習！\n\n答對：${stats.correctCount}題\n答錯：${stats.wrongCount}題\n準確率：${Math.round(stats.accuracy)}%`,
      [
        {
          text: '返回主頁',
          onPress: () => {
            endSession();
            router.replace('/');
          }
        }
      ]
    );
  };

  const handleEndSession = () => {
    Alert.alert(
      '結束複習',
      `本次複習統計：\n\n已完成：${stats.answeredCount} 題\n答對：${stats.correctCount} 題\n答錯：${stats.wrongCount} 題\n準確率：${Math.round(stats.accuracy)}%\n\n確定要結束複習嗎？`,
      [
        { text: '取消', style: 'cancel' },
        {
          text: '結束複習',
          style: 'destructive',
          onPress: () => {
            endSession();
            router.replace('/');
          }
        }
      ]
    );
  };

  const isLastQuestion = currentIndex >= questions.length - 1;

  return (
    <SafeAreaView style={styles.container} edges={['top', 'bottom', 'left', 'right']}>
      <StatusBar style="auto" />

      {/* Header with close button and quick answer toggle (matching practice) */}
      <View style={styles.header}>
        <View style={styles.headerLeft}>
          <TouchableOpacity onPress={() => handleEndSession()} className="pr-2">
            <Ionicons name="chevron-back" size={28} color="black" />
          </TouchableOpacity>
        </View>
        
        {/* Centered Stats */}
        <View style={styles.headerCenter}>
          {renderMinimalisticStats()}
        </View>

        <View style={styles.headerRight}>
          <Button
            title="快速回答"
            onPress={() => setQuickAnswerMode(!quickAnswerMode)}
            variant="outline"
            size="small"
            style={quickAnswerMode ? [styles.quickAnswerBadge, styles.quickAnswerBadgeActive] as any : styles.quickAnswerBadge}
            textStyle={quickAnswerMode ? styles.quickAnswerBadgeTextActive : styles.quickAnswerBadgeText}
          />
        </View>
      </View>

      <ScrollView style={styles.scrollContainer} showsVerticalScrollIndicator={false}>
        {/* Question Card */}
        <QuestionCard
          question={currentQuestion}
          questionNumber={currentIndex + 1}
        />

        {/* Options List */}
        <OptionsList
          options={currentQuestion.options}
          selectedOption={selectedAnswer !== null ? selectedAnswer : undefined}
          onOptionSelect={handleAnswerSelect}
          showAnswer={showAnswer}
          disabled={showAnswer}
        />
      </ScrollView>

      {/* Sticky bottom single action (matching practice) */}
      <View style={styles.stickyBottomContainer}>
        {(!showAnswer && !quickAnswerMode) ? (
          <Button
            title="提交答案"
            size="medium"
            onPress={() => handleSubmitAnswer()}
            disabled={selectedAnswer === null}
            style={styles.fullWidthButton}
          />
        ) : showAnswer ? (
          <View style={styles.navigationButtons}>
            <Button
              title={isLastQuestion ? "完成複習" : "下一題"}
              onPress={handleNext}
              style={styles.nextButton}
            />
          </View>
        ) : null}
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: COLORS.BACKGROUND,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.BORDER,
  },
  headerLeft: {
    flex: 1,
    justifyContent: 'center',
  },
  headerCenter: {
    flex: 2,
    alignItems: 'center',
  },
  headerRight: {
    flex: 1,
    alignItems: 'flex-end',
    justifyContent: 'center',
  },
  closeButton: {
    width: 36,
    height: 36,
    padding: 0,
    margin: 0,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
    borderColor: COLORS.ACCENT,
    borderWidth: 1,
    backgroundColor: 'transparent',
  },
  closeButtonText: {
    fontSize: 24,
    fontWeight: '700',
    color: COLORS.ACCENT,
  },
  quickAnswerBadge: {
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 12,
    minHeight: 0,
    backgroundColor: 'transparent',
    borderColor: COLORS.ACCENT,
  },
  quickAnswerBadgeActive: {
    backgroundColor: COLORS.ACCENT,
  },
  quickAnswerBadgeText: {
    fontSize: 12,
    color: COLORS.ACCENT,
    fontWeight: '600',
  },
  quickAnswerBadgeTextActive: {
    fontSize: 12,
    color: '#ffffff',
    fontWeight: '600',
  },
  minimalisticStats: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    width: '100%',
  },
  remainingContainer: {
    minWidth: 60,
  },
  remainingText: {
    fontSize: 12,
    fontWeight: '600',
    color: COLORS.TEXT,
    textAlign: 'center',
  },
  scoreContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    gap: 8,
  },
  correctNumber: {
    fontSize: 14,
    fontWeight: '600',
    color: COLORS.SUCCESS,
    minWidth: 24,
    textAlign: 'center',
  },
  progressBarContainer: {
    flex: 1,
    height: 8,
    backgroundColor: COLORS.BORDER,
    borderRadius: 4,
    overflow: 'hidden',
  },
  progressBarBg: {
    flex: 1,
    backgroundColor: COLORS.ERROR,
  },
  progressBarCorrect: {
    height: '100%',
    backgroundColor: COLORS.SUCCESS,
  },
  wrongNumber: {
    fontSize: 14,
    fontWeight: '600',
    color: COLORS.ERROR,
    minWidth: 24,
    textAlign: 'center',
  },
  scrollContainer: {
    flex: 1,
    paddingHorizontal: 16,
    paddingVertical: 16,
  },
  stickyBottomContainer: {
    paddingHorizontal: 16,
    paddingVertical: 16,
    backgroundColor: COLORS.BACKGROUND,
    borderTopWidth: 1,
    borderTopColor: COLORS.BORDER,
  },
  fullWidthButton: {
    width: '100%',
    backgroundColor: COLORS.ACCENT,
  },
  navigationButtons: {
    flexDirection: 'row',
    gap: 12,
  },
  nextButton: {
    width: '100%',
    backgroundColor: COLORS.ACCENT,
  },
}); 