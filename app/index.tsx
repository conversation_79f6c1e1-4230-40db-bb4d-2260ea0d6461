import { router, useFocusEffect } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import React, { useCallback, useEffect, useState } from 'react';
import { ActivityIndicator, Alert, ScrollView, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

import { MaterialIcons } from '@expo/vector-icons';
import {
  ActionButtons,
  TopStatusDashboard
} from '../src/components/dashboard';
import VolumePracticeCard from '../src/components/dashboard/VolumePracticeCard';
import { useDatabase } from '../src/hooks/useDatabase';
import { useStatistics } from '../src/hooks/useStatistics';
import { clearAllData } from '../src/services/database';
import { useAppStore } from '../src/store/useAppStore';
import { usePreferencesStore } from '../src/store/usePreferencesStore';
import { COLORS } from '../src/utils/constants';

export default function DashboardScreen() {
  const { isInitializing, setInitializing } = useAppStore();
  const { isInitialized, error: dbError } = useDatabase();
  const { loadPreferences, isLoaded } = usePreferencesStore();
  const { 
    overallStats, 
    predictedPassRate, 
    volumeDisplayStats, 
    loading: statsLoading, 
    error: statsError,
    refreshStats
  } = useStatistics();
  const [loading, setLoading] = useState(true);



  useEffect(() => {
    const initialize = async () => {
      // Load preferences on app start
      if (!isLoaded) {
        await loadPreferences();
      }
      
      if (isInitialized && !dbError) {
        setInitializing(false);
        setLoading(false);
      } else if (dbError) {
        setLoading(false);
        Alert.alert('數據庫錯誤', dbError);
      }
    };
    
    initialize();
  }, [isInitialized, dbError, setInitializing, isLoaded, loadPreferences]);

  // Show error if statistics failed to load
  useEffect(() => {
    if (statsError) {
      Alert.alert('統計數據錯誤', statsError);
    }
  }, [statsError]);

  // Refresh statistics when screen comes into focus (e.g., returning from practice)
  useFocusEffect(
    useCallback(() => {
      if (isInitialized && !dbError) {
        refreshStats();
      }
    }, [isInitialized, dbError, refreshStats])
  );

  const handleStartExam = () => {
    router.push('/exam');
  };

  const handleStartWrongQuestionsReview = () => {
    router.push('/review');
  };

  const handleClearAllData = async () => {
    try {
      await clearAllData();
      // Refresh stats after clearing data
      refreshStats();
    } catch {
      // Error already handled in clearAllData function
    }
  };

  // Show fallback if no data is available
  if (!overallStats || !predictedPassRate || !volumeDisplayStats) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>無法載入統計數據</Text>
          <Text style={styles.errorSubText}>請檢查您的網路連接或稍後再試</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="auto" backgroundColor={COLORS.BACKGROUND} />
      
      <ScrollView
        contentContainerStyle={styles.scrollContainer}
        showsVerticalScrollIndicator={false}
      >
        {/* 1. 歡迎信息 */}
        <View style={styles.welcomeSection}>
          <View style={styles.titleRow}>
            <Text style={styles.welcomeText}>澳門駕駛理論考試</Text>
            {(isInitializing || loading || statsLoading) && (
              <ActivityIndicator 
                size="small"
                color={COLORS.TEXT} 
                style={styles.loadingIndicator}
              />
            )}
          </View>
        </View>

        {/* 2. 頂部狀態儀表板 */}
        <TopStatusDashboard
          stats={overallStats}
          prediction={predictedPassRate}
          userName="學員"
        />

        {/* 3. 主要操作按鈕 */}
        <ActionButtons
          onStartExam={handleStartExam}
          onStartWrongQuestionsReview={handleStartWrongQuestionsReview}
        />

        {/* 4. 各冊進度 */}
        <View style={styles.volumesSection}>
          <Text style={styles.sectionTitle}>按冊練習</Text>
          <View style={styles.volumeGrid}>
            {volumeDisplayStats.map((stats) => (
              <View key={stats.volume} style={styles.volumeCardContainer}>
                <VolumePracticeCard
                  volumeStats={stats}
                />
              </View>
            ))}
          </View>
        </View>

        {/* Clear All Data Button */}
        <View style={styles.clearDataSection}>
          <TouchableOpacity
            style={styles.clearDataButton}
            onPress={handleClearAllData}
            activeOpacity={0.7}
          >
            <MaterialIcons name="delete-outline" size={20} color={COLORS.ERROR} />
            <Text style={styles.clearDataText}>清除所有數據</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND,
  },
  scrollContainer: {
    paddingVertical: 24,
    paddingHorizontal: 20,
  },
  welcomeSection: {
    marginBottom: 20,
  },
  titleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  welcomeText: {
    fontSize: 26,
    fontWeight: 'bold',
    color: COLORS.TEXT,
    // textAlign: 'center',
    letterSpacing: 0.5,
  },
  loadingIndicator: {
    marginRight: 10,
  },
  volumesSection: {
    marginTop: 28,
  },
  sectionTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    color: COLORS.TEXT,
    marginBottom: 20,
    letterSpacing: 0.3,
  },
  volumeGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    gap: 12,
  },
  volumeCardContainer: {
    width: '48%',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  errorText: {
    fontSize: 18,
    fontWeight: '600',
    color: COLORS.TEXT,
    textAlign: 'center',
    marginBottom: 8,
  },
  errorSubText: {
    fontSize: 14,
    color: COLORS.SECONDARY_TEXT,
    textAlign: 'center',
  },
  clearDataSection: {
    marginTop: 40,
    marginBottom: 20,
    alignItems: 'center',
  },
  clearDataButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderWidth: 1,
    borderColor: COLORS.ERROR,
    borderRadius: 8,
    backgroundColor: 'transparent',
    gap: 8,
  },
  clearDataText: {
    fontSize: 14,
    color: COLORS.ERROR,
    fontWeight: '500',
  },
});